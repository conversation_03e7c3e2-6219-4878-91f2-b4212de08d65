import type { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/contexts/AuthContext';
import { CartProvider } from '@/contexts/CartContext';
import { ToastProvider } from '@/components/ui/Toaster';

const inter = Inter({ subsets: ['latin'] });

// metadataBase is now set inside the metadata object

// Define viewport separately (Next.js 13+ requirement)
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: '#3b82f6',
};

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  title: 'Home Services - Professional Services at Your Doorstep',
  description: 'Book professional home services including cleaning, plumbing, electrical work, and more. Fast, reliable, and affordable services.',
  keywords: 'home services, cleaning, plumbing, electrical, repair, maintenance, professional services',
  authors: [{ name: 'Home Services Team' }],
  creator: 'Home Services',
  publisher: 'Home Services',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Home Services',
  },
  openGraph: {
    type: 'website',
    siteName: 'Home Services',
    title: 'Home Services - Professional Services at Your Doorstep',
    description: 'Book professional home services including cleaning, plumbing, electrical work, and more.',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Home Services',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Home Services - Professional Services at Your Doorstep',
    description: 'Book professional home services including cleaning, plumbing, electrical work, and more.',
    images: ['/og-image.jpg'],
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Home Services" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#3b82f6" />
        <meta name="msapplication-tap-highlight" content="no" />
      </head>
      <body className={inter.className}>
        <ToastProvider>
          <AuthProvider>
            <CartProvider>
              <div className="min-h-screen bg-gray-50">
                {children}
              </div>
            </CartProvider>
          </AuthProvider>
        </ToastProvider>
      </body>
    </html>
  );
}
