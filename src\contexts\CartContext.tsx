'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Cart, CartSummary, Service } from '@/types/api';
import { cartApi } from '@/lib/api';
import { useAuth } from './AuthContext';

interface CartContextType {
  cart: Cart | null;
  cartSummary: CartSummary | null;
  isLoading: boolean;
  addToCart: (service: Service, quantity?: number) => Promise<void>;
  updateCartItem: (itemId: number, quantity: number) => Promise<void>;
  removeCartItem: (itemId: number) => Promise<void>;
  clearCart: () => Promise<void>;
  applyCoupon: (couponCode: string) => Promise<void>;
  removeCoupon: () => Promise<void>;
  refreshCart: () => Promise<void>;
  getTotalItems: () => number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

interface CartProviderProps {
  children: ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [cart, setCart] = useState<Cart | null>(null);
  const [cartSummary, setCartSummary] = useState<CartSummary | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { isAuthenticated } = useAuth();

  // Fetch cart data
  const refreshCart = async () => {
    setIsLoading(true);
    try {
      const [cartData, summaryData] = await Promise.all([
        cartApi.getCart(),
        cartApi.getCartSummary(),
      ]);
      setCart(cartData as Cart);
      setCartSummary(summaryData as CartSummary);
    } catch (error) {
      console.error('Failed to fetch cart:', error);
      // Initialize empty cart state
      setCart(null);
      setCartSummary(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize cart on mount and when auth state changes
  useEffect(() => {
    refreshCart();
  }, [isAuthenticated]);

  const addToCart = async (service: Service, quantity: number = 1) => {
    try {
      console.log('CartContext addToCart called with:', { serviceId: service.id, quantity, service });
      await cartApi.addToCart(service.id, quantity);
      await refreshCart();
    } catch (error) {
      console.error('Failed to add to cart:', error);
      throw error;
    }
  };

  const updateCartItem = async (itemId: number, quantity: number) => {
    try {
      await cartApi.updateCartItem(itemId, quantity);
      await refreshCart();
    } catch (error) {
      console.error('Failed to update cart item:', error);
      throw error;
    }
  };

  const removeCartItem = async (itemId: number) => {
    try {
      await cartApi.removeCartItem(itemId);
      await refreshCart();
    } catch (error) {
      console.error('Failed to remove cart item:', error);
      throw error;
    }
  };

  const clearCart = async () => {
    try {
      await cartApi.clearCart();
      await refreshCart();
    } catch (error) {
      console.error('Failed to clear cart:', error);
      throw error;
    }
  };

  const applyCoupon = async (couponCode: string) => {
    try {
      await cartApi.applyCoupon(couponCode);
      await refreshCart();
    } catch (error) {
      console.error('Failed to apply coupon:', error);
      throw error;
    }
  };

  const removeCoupon = async () => {
    try {
      await cartApi.removeCoupon();
      await refreshCart();
    } catch (error) {
      console.error('Failed to remove coupon:', error);
      throw error;
    }
  };

  const getTotalItems = (): number => {
    return cartSummary?.items_count || 0;
  };

  const value: CartContextType = {
    cart,
    cartSummary,
    isLoading,
    addToCart,
    updateCartItem,
    removeCartItem,
    clearCart,
    applyCoupon,
    removeCoupon,
    refreshCart,
    getTotalItems,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
