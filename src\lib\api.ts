import { AuthTokens, ApiError } from '@/types/api';

// Use proxy route to avoid CORS issues in development
const API_BASE_URL = process.env.NODE_ENV === 'development'
  ? '/api/proxy'
  : (process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api');

// Token management
let accessToken: string | null = null;
let refreshToken: string | null = null;

export const setTokens = (tokens: AuthTokens) => {
  accessToken = tokens.access;
  refreshToken = tokens.refresh;
  if (typeof window !== 'undefined') {
    localStorage.setItem('refreshToken', tokens.refresh);
  }
};

export const getTokens = (): { access: string | null; refresh: string | null } => {
  if (typeof window !== 'undefined' && !refreshToken) {
    refreshToken = localStorage.getItem('refreshToken');
  }
  return { access: accessToken, refresh: refreshToken };
};

export const clearTokens = () => {
  accessToken = null;
  refreshToken = null;
  if (typeof window !== 'undefined') {
    localStorage.removeItem('refreshToken');
  }
};

// Token refresh function
const refreshAccessToken = async (): Promise<boolean> => {
  const { refresh } = getTokens();
  if (!refresh) return false;

  try {
    const response = await fetch(`${API_BASE_URL}/auth/token/refresh/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refresh }),
    });

    if (response.ok) {
      const data = await response.json();
      setTokens(data);
      return true;
    } else {
      clearTokens();
      return false;
    }
  } catch (error) {
    clearTokens();
    return false;
  }
};

// API request wrapper with automatic token refresh
export const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
  
  const { access } = getTokens();
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(options.headers as Record<string, string>),
  };

  if (access) {
    headers.Authorization = `Bearer ${access}`;
  }

  let response = await fetch(url, {
    ...options,
    headers,
  });

  // If unauthorized and we have a refresh token, try to refresh
  if (response.status === 401 && refreshToken) {
    const refreshed = await refreshAccessToken();
    if (refreshed) {
      const { access: newAccess } = getTokens();
      headers.Authorization = `Bearer ${newAccess}`;
      response = await fetch(url, {
        ...options,
        headers,
      });
    }
  }

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    const error: ApiError = {
      message: errorData.message || errorData.detail || errorData.error || `HTTP ${response.status}`,
      details: errorData,
      status: response.status,
    };
    throw error;
  }

  // Handle empty responses (like DELETE operations)
  if (response.status === 204) {
    return {} as T;
  }

  return response.json();
};

// Specific API functions
export const authApi = {
  // Register with mobile
  registerMobile: (data: { mobile_number: string; name: string; user_type: string }) =>
    apiRequest('/auth/register/mobile/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Send OTP
  sendOTP: (mobile_number: string) =>
    apiRequest('/auth/otp/send/', {
      method: 'POST',
      body: JSON.stringify({ mobile_number }),
    }),

  // Verify OTP
  verifyOTP: (mobile_number: string, otp: string) =>
    apiRequest('/auth/otp/verify/', {
      method: 'POST',
      body: JSON.stringify({ mobile_number, otp }),
    }),

  // Login with mobile/OTP
  loginMobile: (mobile_number: string, otp: string) =>
    apiRequest('/auth/login/mobile/', {
      method: 'POST',
      body: JSON.stringify({ mobile_number, otp }),
    }),

  // Login with email/password
  loginEmail: (email: string, password: string) =>
    apiRequest('/auth/login/email/', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    }),

  // Get user profile
  getProfile: () => apiRequest('/auth/profile/'),

  // Update user profile
  updateProfile: (data: { name?: string; profile_picture?: string }) =>
    apiRequest('/auth/profile/', {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Logout
  logout: () => {
    const { refresh } = getTokens();
    return apiRequest('/auth/logout/', {
      method: 'POST',
      body: JSON.stringify({ refresh }),
    });
  },

  // Address management
  getAddresses: () => apiRequest('/auth/addresses/'),
  
  createAddress: (data: any) =>
    apiRequest('/auth/addresses/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  updateAddress: (id: number, data: any) =>
    apiRequest(`/auth/addresses/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  deleteAddress: (id: number) =>
    apiRequest(`/auth/addresses/${id}/`, {
      method: 'DELETE',
    }),
};

export const catalogueApi = {
  // Categories
  getCategories: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/catalogue/categories/${query}`);
  },

  getCategoryDetail: (slug: string) =>
    apiRequest(`/catalogue/categories/${slug}/`),

  getCategoryTree: () =>
    apiRequest('/catalogue/categories/tree/'),

  getCategoryServices: (slug: string) =>
    apiRequest(`/catalogue/categories/${slug}/services/`),

  // Services
  getServices: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/catalogue/services/${query}`);
  },

  getServiceDetail: (slug: string) =>
    apiRequest(`/catalogue/services/${slug}/`),

  searchServices: (params: Record<string, any>) => {
    const query = new URLSearchParams(params).toString();
    return apiRequest(`/catalogue/services/search/?${query}`);
  },
};

export const cartApi = {
  // Cart operations
  getCart: () => apiRequest('/cart/'),

  getCartSummary: () => apiRequest('/cart/summary/'),

  addToCart: (service: number, quantity: number) =>
    apiRequest('/cart/add/', {
      method: 'POST',
      body: JSON.stringify({ service_id: service, quantity }),
    }),

  updateCartItem: (itemId: number, quantity: number) =>
    apiRequest(`/cart/items/${itemId}/update/`, {
      method: 'PUT',
      body: JSON.stringify({ quantity }),
    }),

  removeCartItem: (itemId: number) =>
    apiRequest(`/cart/items/${itemId}/remove/`, {
      method: 'DELETE',
    }),

  clearCart: () =>
    apiRequest('/cart/clear/', {
      method: 'POST',
    }),

  // Coupon operations
  applyCoupon: (coupon_code: string) =>
    apiRequest('/cart/coupon/apply/', {
      method: 'POST',
      body: JSON.stringify({ coupon_code }),
    }),

  removeCoupon: () =>
    apiRequest('/cart/coupon/remove/', {
      method: 'POST',
    }),
};

export const orderApi = {
  // Orders
  getOrders: () => apiRequest('/orders/'),

  getOrderDetail: (orderNumber: string) =>
    apiRequest(`/orders/${orderNumber}/`),

  createOrder: (data: any) =>
    apiRequest('/orders/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  cancelOrder: (orderNumber: string, cancellation_reason: string) =>
    apiRequest(`/orders/${orderNumber}/cancel/`, {
      method: 'POST',
      body: JSON.stringify({ cancellation_reason }),
    }),
};

export const paymentApi = {
  // Payments
  initiatePayment: (data: { order_id: string; payment_method: string; amount: string }) =>
    apiRequest('/payments/initiate/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  handleRazorpayCallback: (data: any) =>
    apiRequest('/payments/razorpay/callback/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  getPaymentStatus: (transactionId: string) =>
    apiRequest(`/payments/status/${transactionId}/`),
};

export const couponApi = {
  validateCoupon: (coupon_code: string, cart_amount: string) =>
    apiRequest('/coupons/validate/', {
      method: 'POST',
      body: JSON.stringify({ coupon_code, cart_amount }),
    }),
};
