'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { Search, ArrowRight, Star, Clock, Shield, Users } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { catalogueApi } from '@/lib/api';
import { Category } from '@/types/api';

export default function HomePage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const data = await catalogueApi.getCategories({ level: 0 });
        console.log('Homepage categories data:', data);

        // Handle paginated response structure
        if (data && typeof data === 'object' && 'results' in data && Array.isArray(data.results)) {
          setCategories(data.results as Category[]);
        } else if (Array.isArray(data)) {
          // Fallback for non-paginated response
          setCategories(data as Category[]);
        } else {
          console.warn('Categories data is not in expected format:', data);
          setCategories([]);
        }
      } catch (error) {
        console.error('Failed to fetch categories:', error);
        // Temporary fallback data while backend is being fixed
        const fallbackCategories: Category[] = [
          {
            id: 1,
            name: 'Cleaning',
            slug: 'cleaning',
            description: 'Professional cleaning services for your home',
            level: 0,
            services_count: 5,
            is_active: true,
          },
          {
            id: 2,
            name: 'Plumbing',
            slug: 'plumbing',
            description: 'Expert plumbing repairs and installations',
            level: 0,
            services_count: 8,
            is_active: true,
          },
          {
            id: 3,
            name: 'Electrical',
            slug: 'electrical',
            description: 'Safe and reliable electrical services',
            level: 0,
            services_count: 6,
            is_active: true,
          },
          {
            id: 4,
            name: 'AC Service',
            slug: 'ac-service',
            description: 'AC installation, repair, and maintenance',
            level: 0,
            services_count: 4,
            is_active: true,
          },
        ];
        setCategories(fallbackCategories);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
          <div className="text-center">
            <h1 className="text-4xl lg:text-6xl font-bold mb-6">
              Professional Home Services
              <span className="block text-primary-200">At Your Doorstep</span>
            </h1>
            <p className="text-xl lg:text-2xl text-primary-100 mb-8 max-w-3xl mx-auto">
              Book trusted professionals for cleaning, repairs, maintenance, and more. 
              Quality service guaranteed.
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <form onSubmit={handleSearch} className="flex">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="What service do you need?"
                    className="w-full px-6 py-4 text-lg text-gray-900 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary-300"
                  />
                  <Search className="absolute right-4 top-4 h-6 w-6 text-gray-400" />
                </div>
                <button
                  type="submit"
                  className="px-8 py-4 bg-secondary-600 hover:bg-secondary-700 text-white font-semibold rounded-r-lg transition-colors"
                >
                  Search
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Verified Professionals</h3>
              <p className="text-gray-600">All service providers are background-checked and verified</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">On-Time Service</h3>
              <p className="text-gray-600">Punctual service delivery at your scheduled time</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Quality Guaranteed</h3>
              <p className="text-gray-600">100% satisfaction guarantee on all services</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Trusted by Thousands</h3>
              <p className="text-gray-600">Join thousands of satisfied customers</p>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Popular Services
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Choose from our wide range of professional home services
            </p>
          </div>

          {isLoading ? (
            <div className="flex justify-center">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {categories && categories.length > 0 ? categories.map((category) => (
                <Link
                  key={category.id}
                  href={`/categories/${category.slug}`}
                  className="card-hover p-6 text-center group"
                >
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors">
                    {category.image ? (
                      <Image
                        src={category.image}
                        alt={category.name}
                        width={32}
                        height={32}
                        className="rounded-full"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-primary-600 rounded-full"></div>
                    )}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {category.name}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    {category.description}
                  </p>
                  <div className="flex items-center justify-center text-primary-600 group-hover:text-primary-700">
                    <span className="text-sm font-medium">
                      {category.services_count} services
                    </span>
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </div>
                </Link>
              )) : (
                <div className="text-center py-12">
                  <p className="text-gray-600">No categories available at the moment.</p>
                </div>
              )}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            Ready to Book a Service?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Get started today and experience the convenience of professional home services
          </p>
          <Link
            href="/services"
            className="inline-flex items-center px-8 py-4 bg-white text-primary-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors"
          >
            Browse All Services
            <ArrowRight className="h-5 w-5 ml-2" />
          </Link>
        </div>
      </section>
    </MainLayout>
  );
}
