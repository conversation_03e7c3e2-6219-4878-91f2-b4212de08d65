import { ApiError } from '@/types/api';

/**
 * Check if an API error indicates an unregistered mobile number
 */
export const isUnregisteredMobileError = (error: ApiError): boolean => {
  if (error.status !== 400) return false;
  
  const message = error.message?.toLowerCase() || '';
  const detailMessage = error.details?.detail
    ? (Array.isArray(error.details.detail) ? error.details.detail.join(' ') : String(error.details.detail)).toLowerCase()
    : '';
  const errorMessage = error.details?.error
    ? (Array.isArray(error.details.error) ? error.details.error.join(' ') : String(error.details.error)).toLowerCase()
    : '';
  
  const unregisteredKeywords = [
    'not registered',
    'unregistered',
    'mobile number not found',
    'user not found',
    'does not exist',
    'no account found',
    'please register'
  ];
  
  return unregisteredKeywords.some(keyword => 
    message.includes(keyword) || 
    detailMessage.includes(keyword) || 
    errorMessage.includes(keyword)
  );
};

/**
 * Check if an API error indicates an already registered mobile number
 */
export const isAlreadyRegisteredError = (error: ApiError): boolean => {
  if (error.status !== 400) return false;
  
  const message = error.message?.toLowerCase() || '';
  const detailMessage = error.details?.detail
    ? (Array.isArray(error.details.detail) ? error.details.detail.join(' ') : String(error.details.detail)).toLowerCase()
    : '';
  const errorMessage = error.details?.error
    ? (Array.isArray(error.details.error) ? error.details.error.join(' ') : String(error.details.error)).toLowerCase()
    : '';
  
  const registeredKeywords = [
    'already registered',
    'already exists',
    'mobile number already in use',
    'user already exists',
    'duplicate'
  ];
  
  return registeredKeywords.some(keyword => 
    message.includes(keyword) || 
    detailMessage.includes(keyword) || 
    errorMessage.includes(keyword)
  );
};

/**
 * Check if an API error indicates invalid OTP
 */
export const isInvalidOTPError = (error: ApiError): boolean => {
  if (error.status !== 400) return false;
  
  const message = error.message?.toLowerCase() || '';
  const detailMessage = error.details?.detail
    ? (Array.isArray(error.details.detail) ? error.details.detail.join(' ') : String(error.details.detail)).toLowerCase()
    : '';
  const errorMessage = error.details?.error
    ? (Array.isArray(error.details.error) ? error.details.error.join(' ') : String(error.details.error)).toLowerCase()
    : '';
  
  const otpKeywords = [
    'invalid otp',
    'incorrect otp',
    'otp expired',
    'otp not found',
    'wrong otp'
  ];
  
  return otpKeywords.some(keyword => 
    message.includes(keyword) || 
    detailMessage.includes(keyword) || 
    errorMessage.includes(keyword)
  );
};

/**
 * Check if the error is due to rate limiting (too many requests)
 */
export const isRateLimitError = (error: ApiError): boolean => {
  // Check for 429 status code
  if (error.status === 429) {
    return true;
  }

  const message = error.message ? error.message.toLowerCase() : '';
  const detailMessage = error.details?.detail
    ? (Array.isArray(error.details.detail) ? error.details.detail.join(' ') : String(error.details.detail)).toLowerCase()
    : '';
  const errorMessage = error.details?.error
    ? (Array.isArray(error.details.error) ? error.details.error.join(' ') : String(error.details.error)).toLowerCase()
    : '';

  const rateLimitKeywords = [
    'too many requests',
    'too many otp requests',
    'rate limit',
    'try again later',
    'please wait',
    'request limit exceeded',
    'rate exceeded'
  ];

  return rateLimitKeywords.some(keyword =>
    message.includes(keyword) ||
    detailMessage.includes(keyword) ||
    errorMessage.includes(keyword)
  );
};

/**
 * Get a user-friendly error message from an API error
 */
export const getUserFriendlyErrorMessage = (error: ApiError): string => {
  if (isUnregisteredMobileError(error)) {
    return 'This mobile number is not registered. You will be redirected to create an account.';
  }

  if (isAlreadyRegisteredError(error)) {
    return 'This mobile number is already registered. Please login instead.';
  }

  if (isInvalidOTPError(error)) {
    return 'Invalid or expired OTP. Please try again.';
  }

  if (isRateLimitError(error)) {
    return 'Too many OTP requests. Please wait for some time and try again.';
  }

  // Return the original error message or a generic one
  return error.message || 'An unexpected error occurred. Please try again.';
};
