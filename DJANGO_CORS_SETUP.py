# Add this to your Django settings.py file

# Add 'corsheaders' to INSTALLED_APPS
INSTALLED_APPS = [
    # ... your existing apps
    'corsheaders',
    # ... rest of your apps
]

# Add CorsMiddleware to MIDDLEWARE (should be at the top)
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    # ... rest of your middleware
]

# CORS settings for development
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

# For development only - allows all origins (less secure but easier for dev)
CORS_ALLOW_ALL_ORIGINS = True

# Allow credentials to be included in CORS requests
CORS_ALLOW_CREDENTIALS = True

# Specify which headers can be used during the actual request
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# Specify which methods are allowed for CORS requests
CORS_ALLOWED_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# For production, you should set specific origins instead of CORS_ALLOW_ALL_ORIGINS = True
# CORS_ALLOWED_ORIGINS = [
#     "https://yourdomain.com",
#     "https://www.yourdomain.com",
# ]
