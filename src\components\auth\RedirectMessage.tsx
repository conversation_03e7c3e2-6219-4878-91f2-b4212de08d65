'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { AlertCircle, UserPlus, LogIn } from 'lucide-react';

interface RedirectMessageProps {
  type: 'login' | 'register';
}

export const RedirectMessage: React.FC<RedirectMessageProps> = ({ type }) => {
  const [showMessage, setShowMessage] = useState(false);
  const [message, setMessage] = useState('');
  const searchParams = useSearchParams();

  useEffect(() => {
    const mobile = searchParams.get('mobile');
    const fromRedirect = searchParams.get('from');

    if (mobile && fromRedirect) {
      setShowMessage(true);
      
      if (type === 'register' && fromRedirect === 'login') {
        setMessage(`Mobile number ${mobile} is not registered. Please create an account to continue.`);
      } else if (type === 'login' && fromRedirect === 'register') {
        setMessage(`Mobile number ${mobile} is already registered. Please login to continue.`);
      }

      // Auto-hide message after 10 seconds
      const timer = setTimeout(() => {
        setShowMessage(false);
      }, 10000);

      return () => clearTimeout(timer);
    }
  }, [searchParams, type]);

  if (!showMessage) return null;

  return (
    <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {type === 'register' ? (
            <UserPlus className="h-5 w-5 text-blue-600" />
          ) : (
            <LogIn className="h-5 w-5 text-blue-600" />
          )}
        </div>
        <div className="flex-1">
          <h3 className="text-sm font-medium text-blue-900 mb-1">
            {type === 'register' ? 'Registration Required' : 'Login Required'}
          </h3>
          <p className="text-sm text-blue-700">{message}</p>
        </div>
        <button
          onClick={() => setShowMessage(false)}
          className="flex-shrink-0 text-blue-400 hover:text-blue-600"
        >
          <span className="sr-only">Close</span>
          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};
