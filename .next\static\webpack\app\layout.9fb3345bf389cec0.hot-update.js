"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/CartContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/CartContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: function() { return /* binding */ CartProvider; },\n/* harmony export */   useCart: function() { return /* binding */ useCart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useCart,CartProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useCart = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error(\"useCart must be used within a CartProvider\");\n    }\n    return context;\n};\n_s(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst CartProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cartSummary, setCartSummary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAuthenticated } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Fetch cart data\n    const refreshCart = async ()=>{\n        setIsLoading(true);\n        try {\n            const [cartData, summaryData] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.getCart(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.getCartSummary()\n            ]);\n            setCart(cartData);\n            setCartSummary(summaryData);\n        } catch (error) {\n            console.error(\"Failed to fetch cart:\", error);\n            // Initialize empty cart state\n            setCart(null);\n            setCartSummary(null);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Initialize cart on mount and when auth state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        refreshCart();\n    }, [\n        isAuthenticated\n    ]);\n    const addToCart = async function(service) {\n        let quantity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.addToCart(service.id, quantity);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to add to cart:\", error);\n            throw error;\n        }\n    };\n    const updateCartItem = async (itemId, quantity)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.updateCartItem(itemId, quantity);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to update cart item:\", error);\n            throw error;\n        }\n    };\n    const removeCartItem = async (itemId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.removeCartItem(itemId);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to remove cart item:\", error);\n            throw error;\n        }\n    };\n    const clearCart = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.clearCart();\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to clear cart:\", error);\n            throw error;\n        }\n    };\n    const applyCoupon = async (couponCode)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.applyCoupon(couponCode);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to apply coupon:\", error);\n            throw error;\n        }\n    };\n    const removeCoupon = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.removeCoupon();\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to remove coupon:\", error);\n            throw error;\n        }\n    };\n    const getTotalItems = ()=>{\n        return (cartSummary === null || cartSummary === void 0 ? void 0 : cartSummary.items_count) || 0;\n    };\n    const value = {\n        cart,\n        cartSummary,\n        isLoading,\n        addToCart,\n        updateCartItem,\n        removeCartItem,\n        clearCart,\n        applyCoupon,\n        removeCoupon,\n        refreshCart,\n        getTotalItems\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\contexts\\\\CartContext.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CartProvider, \"F9YQ2jhR0j9KfNAPU2KBxUpgfUc=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = CartProvider;\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/CartContext.tsx\n"));

/***/ })

});