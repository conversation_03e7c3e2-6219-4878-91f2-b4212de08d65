# Testing Guide - Authentication Flow Updates

This guide provides step-by-step instructions to test the new authentication flow with unregistered mobile number handling.

## 🧪 **Prerequisites**

1. **Backend API Running**: Ensure your Django backend is running with the updated OTP validation
2. **Frontend Running**: Start the Next.js development server (`npm run dev`)
3. **Test Data**: Have both registered and unregistered mobile numbers ready for testing

## 📋 **Test Scenarios**

### **Test 1: Unregistered Mobile Number Login Flow**

**Objective**: Verify that users with unregistered mobile numbers are redirected to registration

**Steps**:
1. Open browser to `http://localhost:3000/auth/login`
2. Enter an **unregistered mobile number** (e.g., `**********`)
3. Click "Send OTP" button
4. **Expected Results**:
   - ❌ Error toast appears: "Mobile number not registered"
   - ⏱️ After 2 seconds, automatic redirect to registration page
   - 📱 Mobile number is pre-filled in registration form
   - 💬 Blue info message appears: "Mobile number ********** is not registered. Please create an account to continue."

**Verification Points**:
- [ ] Error message is clear and actionable
- [ ] Redirect happens automatically
- [ ] Mobile number is pre-filled correctly
- [ ] Contextual message is displayed
- [ ] Message can be manually dismissed

---

### **Test 2: Already Registered Mobile Number Registration Flow**

**Objective**: Verify that users trying to register with existing mobile numbers are redirected to login

**Steps**:
1. Open browser to `http://localhost:3000/auth/register`
2. Enter a **registered mobile number** (e.g., `**********`)
3. Enter any name (e.g., "Test User")
4. Click "Create Account" button
5. **Expected Results**:
   - ❌ Error toast appears: "Mobile number already registered"
   - ⏱️ After 2 seconds, automatic redirect to login page
   - 📱 Mobile number is pre-filled in login form
   - 💬 Blue info message appears: "Mobile number ********** is already registered. Please login to continue."

**Verification Points**:
- [ ] Error message is clear and actionable
- [ ] Redirect happens automatically
- [ ] Mobile number is pre-filled correctly
- [ ] Contextual message is displayed
- [ ] Message can be manually dismissed

---

### **Test 3: Successful Registration Flow**

**Objective**: Verify that new users can register successfully

**Steps**:
1. Open browser to `http://localhost:3000/auth/register`
2. Enter an **unregistered mobile number** (e.g., `**********`)
3. Enter a name (e.g., "New User")
4. Click "Create Account" button
5. **Expected Results**:
   - ✅ Success toast: "Registration successful! Please verify your mobile number."
   - 📱 OTP input form appears
   - ⏱️ 60-second countdown timer starts
   - 📨 OTP is sent to the mobile number (check backend logs)

**Verification Points**:
- [ ] Registration succeeds without errors
- [ ] OTP verification step is shown
- [ ] Timer countdown works correctly
- [ ] Resend button appears after timer expires

---

### **Test 4: Successful Login Flow**

**Objective**: Verify that registered users can login successfully

**Steps**:
1. Open browser to `http://localhost:3000/auth/login`
2. Enter a **registered mobile number** (e.g., `**********`)
3. Click "Send OTP" button
4. **Expected Results**:
   - ✅ Success toast: "OTP sent successfully"
   - 📱 OTP input form appears
   - ⏱️ 60-second countdown timer starts
   - 📨 OTP is sent to the mobile number

5. Enter the correct OTP and click "Verify & Login"
6. **Expected Results**:
   - ✅ Success toast: "Login successful"
   - 🏠 Redirect to homepage or intended destination

**Verification Points**:
- [ ] OTP is sent successfully
- [ ] OTP verification works
- [ ] Login completes successfully
- [ ] User is redirected correctly

---

### **Test 5: Invalid OTP Handling**

**Objective**: Verify that invalid OTP attempts are handled gracefully

**Steps**:
1. Complete the OTP send process (from Test 3 or 4)
2. Enter an **incorrect OTP** (e.g., `000000`)
3. Click "Verify" button
4. **Expected Results**:
   - ❌ Error toast: "Invalid OTP" with descriptive message
   - 📱 OTP input field remains active
   - 🔄 User can try again or request new OTP

**Verification Points**:
- [ ] Clear error message for invalid OTP
- [ ] Form remains usable after error
- [ ] User can retry without page refresh

---

### **Test 6: Resend OTP Functionality**

**Objective**: Verify that OTP resend works correctly for both registered and unregistered numbers

**Steps for Registered Number**:
1. Start login process with registered mobile number
2. Wait for timer to expire (or test immediately if timer allows)
3. Click "Resend OTP" button
4. **Expected Results**:
   - ✅ Success toast: "OTP resent successfully"
   - ⏱️ Timer resets to 60 seconds
   - 📨 New OTP is sent

**Steps for Unregistered Number** (if somehow reached OTP step):
1. If OTP step is reached with unregistered number
2. Click "Resend OTP" button
3. **Expected Results**:
   - ❌ Error toast: "Mobile number not registered"
   - 🔄 Redirect to registration page

**Verification Points**:
- [ ] Resend works for valid numbers
- [ ] Resend validates registration status
- [ ] Timer resets correctly
- [ ] Appropriate error handling

---

### **Test 7: URL Parameter Handling**

**Objective**: Verify that URL parameters work correctly for pre-filling and redirects

**Test 7a: Pre-fill Mobile Number**:
1. Navigate to `http://localhost:3000/auth/login?mobile=**********`
2. **Expected Results**:
   - 📱 Mobile number field is pre-filled with `**********`

**Test 7b: Redirect After Login**:
1. Navigate to `http://localhost:3000/auth/login?redirect=/cart`
2. Complete successful login
3. **Expected Results**:
   - 🛒 User is redirected to `/cart` instead of homepage

**Test 7c: Cross-page Redirect Messages**:
1. Navigate to `http://localhost:3000/auth/login?mobile=**********&from=register`
2. **Expected Results**:
   - 💬 Contextual message appears about coming from registration

**Verification Points**:
- [ ] Mobile number pre-filling works
- [ ] Redirect parameter is respected
- [ ] Cross-page messages display correctly

---

### **Test 8: Mobile Responsiveness**

**Objective**: Verify that all new features work well on mobile devices

**Steps**:
1. Open browser developer tools
2. Switch to mobile device simulation (iPhone/Android)
3. Repeat Tests 1-7 on mobile viewport
4. **Expected Results**:
   - 📱 All forms are touch-friendly
   - 💬 Messages are readable on small screens
   - 🔘 Buttons are appropriately sized
   - ⏱️ Timers and redirects work smoothly

**Verification Points**:
- [ ] Touch interactions work properly
- [ ] Text is readable without zooming
- [ ] Buttons are easily tappable
- [ ] Messages don't overflow screen

---

## 🐛 **Common Issues & Troubleshooting**

### **Issue 1: Error messages not appearing**
- **Check**: Browser console for JavaScript errors
- **Check**: Network tab for API response format
- **Solution**: Verify error detection keywords in `errorUtils.ts`

### **Issue 2: Redirects not working**
- **Check**: Browser console for navigation errors
- **Check**: URL parameters are correctly formatted
- **Solution**: Verify router.push() calls and setTimeout timing

### **Issue 3: Mobile number not pre-filling**
- **Check**: URL parameters are correctly encoded
- **Check**: useSearchParams() is working
- **Solution**: Verify useEffect dependencies and parameter parsing

### **Issue 4: OTP not being sent**
- **Check**: Backend API logs for OTP generation
- **Check**: Network requests in browser dev tools
- **Solution**: Verify backend OTP service configuration

## ✅ **Test Completion Checklist**

After completing all tests, verify:

- [ ] **Error Handling**: All error scenarios show appropriate messages
- [ ] **User Flow**: Users can navigate between login/register seamlessly
- [ ] **Data Persistence**: Form data is preserved during redirects
- [ ] **Mobile Experience**: All features work on mobile devices
- [ ] **Performance**: No unnecessary API calls or delays
- [ ] **Accessibility**: Error messages are screen-reader friendly
- [ ] **Security**: No sensitive data exposed in URLs or console

## 📊 **Test Results Template**

Use this template to document your test results:

```
Test Date: ___________
Tester: ___________
Environment: Development/Staging/Production

Test 1 - Unregistered Mobile Login: ✅ PASS / ❌ FAIL
Notes: ________________________________

Test 2 - Already Registered Registration: ✅ PASS / ❌ FAIL  
Notes: ________________________________

Test 3 - Successful Registration: ✅ PASS / ❌ FAIL
Notes: ________________________________

Test 4 - Successful Login: ✅ PASS / ❌ FAIL
Notes: ________________________________

Test 5 - Invalid OTP: ✅ PASS / ❌ FAIL
Notes: ________________________________

Test 6 - Resend OTP: ✅ PASS / ❌ FAIL
Notes: ________________________________

Test 7 - URL Parameters: ✅ PASS / ❌ FAIL
Notes: ________________________________

Test 8 - Mobile Responsiveness: ✅ PASS / ❌ FAIL
Notes: ________________________________

Overall Result: ✅ PASS / ❌ FAIL
Additional Notes: ________________________________
```

---

**Happy Testing! 🚀**
