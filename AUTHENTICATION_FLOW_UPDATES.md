# Authentication Flow Updates - Unregistered Mobile Number Handling

This document outlines the updates made to the Next.js customer application to handle the new backend API behavior for unregistered mobile numbers.

## 🔄 **Backend API Changes Summary**

The Django backend now validates mobile number registration before sending OTP:

### Before (Old Behavior):
- `POST /api/auth/otp/send/` would send OTP to ANY mobile number
- Login would fail with generic 401 error for non-existent users

### After (New Behavior):
- `POST /api/auth/otp/send/` checks if mobile number is registered first
- **If registered**: Sends OTP and returns success message
- **If not registered**: Returns clear error message without sending OTP

## 🛠 **Frontend Updates Made**

### 1. **Enhanced Error Handling Utilities**

Created `src/lib/errorUtils.ts` with intelligent error detection:

```typescript
// Detects unregistered mobile number errors
export const isUnregisteredMobileError = (error: ApiError): boolean

// Detects already registered mobile number errors  
export const isAlreadyRegisteredError = (error: ApiError): boolean

// Detects invalid OTP errors
export const isInvalidOTPError = (error: ApiError): boolean

// Returns user-friendly error messages
export const getUserFriendlyErrorMessage = (error: ApiError): string
```

### 2. **Smart Login Flow (`src/app/auth/login/page.tsx`)**

#### **Send OTP Handling:**
```typescript
const handleSendOTP = async () => {
  try {
    await authApi.sendOTP(formData.mobile_number);
    // Success: Show OTP input
    setOtpSent(true);
    startOtpTimer();
  } catch (error: any) {
    if (isUnregisteredMobileError(error)) {
      // Redirect to registration with pre-filled mobile number
      showToast({ 
        type: 'error', 
        title: 'Mobile number not registered',
        message: 'This mobile number is not registered. Please sign up first.'
      });
      setTimeout(() => {
        router.push(`/auth/register?mobile=${mobile}&from=login`);
      }, 2000);
    }
  }
};
```

#### **Resend OTP Handling:**
- Same validation logic applied to resend functionality
- Maintains consistency across all OTP operations

#### **URL Parameter Support:**
- Pre-fills mobile number from URL parameter: `/auth/login?mobile=**********`
- Supports redirect parameter: `/auth/login?redirect=/cart`

### 3. **Smart Registration Flow (`src/app/auth/register/page.tsx`)**

#### **Registration Handling:**
```typescript
const handleRegister = async () => {
  try {
    await authApi.registerMobile({
      mobile_number: formData.mobile_number,
      name: formData.name,
      user_type: 'CUSTOMER',
    });
    // Success: Send OTP for verification
    await authApi.sendOTP(formData.mobile_number);
    setStep('verify');
  } catch (error: any) {
    if (isAlreadyRegisteredError(error)) {
      // Redirect to login with pre-filled mobile number
      showToast({
        type: 'error',
        title: 'Mobile number already registered',
        message: 'This mobile number is already registered. Please login instead.'
      });
      setTimeout(() => {
        router.push(`/auth/login?mobile=${mobile}&from=register`);
      }, 2000);
    }
  }
};
```

#### **URL Parameter Support:**
- Pre-fills mobile number from URL parameter: `/auth/register?mobile=**********`
- Supports source tracking: `/auth/register?from=login`

### 4. **User-Friendly Redirect Messages (`src/components/auth/RedirectMessage.tsx`)**

Smart component that shows contextual messages when users are redirected:

```typescript
// Shows when redirected from login to register
"Mobile number ********** is not registered. Please create an account to continue."

// Shows when redirected from register to login  
"Mobile number ********** is already registered. Please login to continue."
```

Features:
- ✅ Auto-dismisses after 10 seconds
- ✅ Manual close button
- ✅ Contextual icons and styling
- ✅ Only shows when appropriate URL parameters are present

## 🔄 **Complete User Journey Flows**

### **Scenario 1: User tries to login with unregistered number**

1. User enters unregistered mobile number on login page
2. Clicks "Send OTP"
3. Backend returns 400 error: "Mobile number not registered"
4. Frontend shows error toast with clear message
5. **Automatic redirect** to registration page after 2 seconds
6. Registration page shows helpful message and pre-fills mobile number
7. User completes registration process

### **Scenario 2: User tries to register with already registered number**

1. User enters registered mobile number on registration page
2. Clicks "Create Account"
3. Backend returns 400 error: "Mobile number already registered"
4. Frontend shows error toast with clear message
5. **Automatic redirect** to login page after 2 seconds
6. Login page shows helpful message and pre-fills mobile number
7. User completes login process

### **Scenario 3: Invalid OTP handling**

1. User enters incorrect OTP
2. Backend returns 400 error: "Invalid OTP"
3. Frontend shows specific "Invalid OTP" error message
4. User can try again or request new OTP

## 🎯 **Benefits of Implementation**

### **✅ Improved User Experience**
- Clear, actionable error messages
- Automatic navigation between login/register
- Pre-filled forms reduce typing
- Contextual help messages

### **✅ Reduced Support Burden**
- Users understand exactly what went wrong
- Self-service resolution for common issues
- Clear guidance on next steps

### **✅ Enhanced Security**
- No OTP sent to unregistered numbers
- Prevents enumeration attacks
- Validates user existence before OTP generation

### **✅ Cost Optimization**
- Reduces unnecessary OTP SMS costs
- Only sends OTPs to valid, registered users

## 🔧 **Technical Implementation Details**

### **Error Detection Logic**

The error detection functions check multiple possible error message formats:

```typescript
const unregisteredKeywords = [
  'not registered',
  'unregistered', 
  'mobile number not found',
  'user not found',
  'does not exist'
];
```

### **Robust Type Handling**

Handles different API response formats:
- String messages: `"Mobile number not registered"`
- Array messages: `["Mobile number not registered"]`
- Nested error objects: `{ detail: "Error message" }`

### **URL Parameter Management**

Smart URL parameter handling for seamless user experience:
- `mobile`: Pre-fills mobile number field
- `from`: Tracks source page for contextual messages
- `redirect`: Preserves intended destination after auth

## 🧪 **Testing Scenarios**

### **Test Case 1: Unregistered Mobile Login**
1. Go to `/auth/login`
2. Enter unregistered mobile number
3. Click "Send OTP"
4. Verify error message appears
5. Verify automatic redirect to registration
6. Verify mobile number is pre-filled
7. Verify contextual message is shown

### **Test Case 2: Already Registered Mobile Registration**
1. Go to `/auth/register`
2. Enter already registered mobile number
3. Fill name and click "Create Account"
4. Verify error message appears
5. Verify automatic redirect to login
6. Verify mobile number is pre-filled
7. Verify contextual message is shown

### **Test Case 3: Invalid OTP**
1. Complete OTP send process
2. Enter incorrect OTP
3. Verify specific "Invalid OTP" error message
4. Verify user can retry

## 📱 **Mobile-First Design**

All error messages and redirects are optimized for mobile users:
- Touch-friendly close buttons
- Readable font sizes
- Appropriate timing for redirects
- Clear visual hierarchy

## 🔮 **Future Enhancements**

Potential improvements for future versions:
- Rate limiting feedback for too many OTP requests
- Progressive enhancement for offline scenarios
- Biometric authentication integration
- Social login fallback options

---

**The authentication flow is now more robust, user-friendly, and aligned with modern UX best practices while maintaining security and cost efficiency.**
