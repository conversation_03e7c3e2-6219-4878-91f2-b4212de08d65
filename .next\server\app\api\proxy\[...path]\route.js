"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/proxy/[...path]/route";
exports.ids = ["app/api/proxy/[...path]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&page=%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproxy%2F%5B...path%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&page=%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproxy%2F%5B...path%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dodob_OneDrive_Documents_vinay_Projects_Home_services_next_js_customer_src_app_api_proxy_path_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/proxy/[...path]/route.ts */ \"(rsc)/./src/app/api/proxy/[...path]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/proxy/[...path]/route\",\n        pathname: \"/api/proxy/[...path]\",\n        filename: \"route\",\n        bundlePath: \"app/api/proxy/[...path]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\api\\\\proxy\\\\[...path]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dodob_OneDrive_Documents_vinay_Projects_Home_services_next_js_customer_src_app_api_proxy_path_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/proxy/[...path]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&page=%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproxy%2F%5B...path%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/proxy/[...path]/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/proxy/[...path]/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\nconst API_BASE_URL = \"http://localhost:8000/api\";\nasync function GET(request, { params }) {\n    try {\n        const { searchParams, pathname } = new URL(request.url);\n        const queryString = searchParams.toString();\n        const apiPath = params.path.join(\"/\");\n        // Preserve trailing slash if it exists in the original request\n        let finalPath = apiPath;\n        if (finalPath.length > 0 && !finalPath.endsWith(\"/\")) {\n            finalPath += \"/\";\n        }\n        const url = `${API_BASE_URL}/${finalPath}${queryString ? `?${queryString}` : \"\"}`;\n        console.log(\"Proxying GET request to:\", url);\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"Backend error:\", response.status, errorText);\n            // Try to parse the error as JSON to preserve the original error structure\n            let errorData;\n            try {\n                errorData = JSON.parse(errorText);\n            } catch  {\n                errorData = {\n                    error: errorText || response.statusText\n                };\n            }\n            // Return the error with the original status code and error details\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(errorData, {\n                status: response.status,\n                headers: {\n                    \"Access-Control-Allow-Origin\": \"*\",\n                    \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n                    \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n                }\n            });\n        }\n        const data = await response.json();\n        console.log(\"Proxy response received for:\", apiPath);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n            status: 200,\n            headers: {\n                \"Access-Control-Allow-Origin\": \"*\",\n                \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n                \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n            }\n        });\n    } catch (error) {\n        console.error(\"Proxy error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message || \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request, { params }) {\n    try {\n        const { searchParams, pathname } = new URL(request.url);\n        const queryString = searchParams.toString();\n        const apiPath = params.path.join(\"/\");\n        // Preserve trailing slash if it exists in the original request\n        let finalPath = apiPath;\n        if (finalPath.length > 0 && !finalPath.endsWith(\"/\")) {\n            finalPath += \"/\";\n        }\n        const url = `${API_BASE_URL}/${finalPath}${queryString ? `?${queryString}` : \"\"}`;\n        const body = await request.text();\n        console.log(\"Proxying POST request to:\", url);\n        const response = await fetch(url, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"Backend error:\", response.status, errorText);\n            // Try to parse the error as JSON to preserve the original error structure\n            let errorData;\n            try {\n                errorData = JSON.parse(errorText);\n            } catch  {\n                errorData = {\n                    error: errorText || response.statusText\n                };\n            }\n            // Return the error with the original status code and error details\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(errorData, {\n                status: response.status,\n                headers: {\n                    \"Access-Control-Allow-Origin\": \"*\",\n                    \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n                    \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n                }\n            });\n        }\n        const data = await response.json();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n            status: response.status,\n            headers: {\n                \"Access-Control-Allow-Origin\": \"*\",\n                \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n                \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n            }\n        });\n    } catch (error) {\n        console.error(\"Proxy error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error.message || \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function OPTIONS() {\n    return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/proxy/[...path]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&page=%2Fapi%2Fproxy%2F%5B...path%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproxy%2F%5B...path%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();