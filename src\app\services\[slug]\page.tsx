'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { ChevronRight, ShoppingCart, Clock, Star, Shield, Award, Users } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { LoadingPage, LoadingButton } from '@/components/ui/LoadingSpinner';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/Toaster';
import { catalogueApi } from '@/lib/api';
import { Service } from '@/types/api';

export default function ServiceDetailPage() {
  const [service, setService] = useState<Service | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [addingToCart, setAddingToCart] = useState(false);
  const [quantity, setQuantity] = useState(1);

  const params = useParams();
  const router = useRouter();
  const { addToCart } = useCart();
  const { isAuthenticated } = useAuth();
  const { showToast } = useToast();

  const slug = params.slug as string;

  useEffect(() => {
    const fetchService = async () => {
      try {
        const serviceData = await catalogueApi.getServiceDetail(slug);
        setService(serviceData as Service);
      } catch (error) {
        console.error('Failed to fetch service:', error);
        showToast({ type: 'error', title: 'Failed to load service' });
      } finally {
        setIsLoading(false);
      }
    };

    fetchService();
  }, [slug, showToast]);

  const handleAddToCart = async () => {
    if (!service) return;

    if (!isAuthenticated) {
      router.push(`/auth/login?redirect=${encodeURIComponent(window.location.pathname)}`);
      return;
    }

    setAddingToCart(true);
    try {
      await addToCart(service, quantity);
      showToast({ type: 'success', title: 'Added to cart', message: `${quantity}x ${service.title}` });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to add to cart', message: error.message });
    } finally {
      setAddingToCart(false);
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <LoadingPage message="Loading service..." />
      </MainLayout>
    );
  }

  if (!service) {
    return (
      <MainLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Service not found</h1>
          <Link href="/" className="btn-primary">
            Go Home
          </Link>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-primary-600">Home</Link>
          <ChevronRight className="h-4 w-4" />
          <Link href={`/categories/${service.category}`} className="hover:text-primary-600">
            {service.category_name}
          </Link>
          <ChevronRight className="h-4 w-4" />
          <span className="text-gray-900 font-medium">{service.title}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Service Image */}
          <div>
            <div className="aspect-square bg-gray-200 rounded-lg overflow-hidden">
              {service.image ? (
                <Image
                  src={service.image}
                  alt={service.title}
                  width={600}
                  height={600}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-primary-100 flex items-center justify-center">
                  <span className="text-primary-600 font-bold text-6xl">
                    {service.title.charAt(0)}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Service Details */}
          <div>
            <div className="mb-6">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{service.title}</h1>
              <p className="text-lg text-gray-600 mb-6">{service.description}</p>

              {/* Service Features */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="flex items-center space-x-2">
                  <Clock className="h-5 w-5 text-primary-600" />
                  <span className="text-sm text-gray-600">Duration: {service.time_to_complete}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Star className="h-5 w-5 text-yellow-400" />
                  <span className="text-sm text-gray-600">4.8 (120 reviews)</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-green-600" />
                  <span className="text-sm text-gray-600">Insured & Verified</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Award className="h-5 w-5 text-blue-600" />
                  <span className="text-sm text-gray-600">Quality Guaranteed</span>
                </div>
              </div>
            </div>

            {/* Pricing */}
            <div className="mb-8">
              <div className="flex items-center space-x-4 mb-4">
                <span className="text-3xl font-bold text-gray-900">₹{service.current_price}</span>
                {service.base_price !== service.current_price && (
                  <>
                    <span className="text-xl text-gray-500 line-through">₹{service.base_price}</span>
                    {service.discount_percentage && (
                      <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm font-medium">
                        {service.discount_percentage}% OFF
                      </span>
                    )}
                  </>
                )}
              </div>
              <p className="text-sm text-gray-600">Price includes all materials and labor</p>
            </div>

            {/* Quantity Selector */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                  disabled={quantity <= 1}
                >
                  -
                </button>
                <span className="w-12 text-center font-medium text-lg">{quantity}</span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  +
                </button>
              </div>
            </div>

            {/* Add to Cart Button */}
            <div className="mb-8">
              <LoadingButton
                onClick={handleAddToCart}
                isLoading={addingToCart}
                className="w-full btn-primary text-lg py-4"
              >
                <ShoppingCart className="h-5 w-5 mr-2" />
                Add to Cart - ₹{(parseFloat(service.current_price) * quantity).toFixed(2)}
              </LoadingButton>
            </div>

            {/* Service Highlights */}
            <div className="border-t pt-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Why Choose This Service?</h3>
              <ul className="space-y-3">
                <li className="flex items-start space-x-3">
                  <Users className="h-5 w-5 text-primary-600 mt-0.5" />
                  <span className="text-gray-600">Experienced and trained professionals</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Shield className="h-5 w-5 text-primary-600 mt-0.5" />
                  <span className="text-gray-600">100% satisfaction guarantee</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Award className="h-5 w-5 text-primary-600 mt-0.5" />
                  <span className="text-gray-600">High-quality materials and equipment</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Clock className="h-5 w-5 text-primary-600 mt-0.5" />
                  <span className="text-gray-600">On-time service delivery</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">What's Included</h3>
            <ul className="space-y-2 text-gray-600">
              <li>• Professional service by trained experts</li>
              <li>• All necessary tools and equipment</li>
              <li>• Quality materials (where applicable)</li>
              <li>• Post-service cleanup</li>
              <li>• Service warranty</li>
            </ul>
          </div>

          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">How It Works</h3>
            <ol className="space-y-2 text-gray-600">
              <li>1. Book your service online</li>
              <li>2. Choose your preferred date and time</li>
              <li>3. Our professional arrives at your location</li>
              <li>4. Service completed to your satisfaction</li>
              <li>5. Make payment after service completion</li>
            </ol>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
